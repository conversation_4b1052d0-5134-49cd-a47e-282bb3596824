@echo off
chcp 65001 >nul
echo ========================================
echo VPN配置网页自动打开器
echo ========================================
echo.

echo [检查] 验证VPN服务器状态...

REM 检查Docker是否运行
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动
    echo 请先安装并启动Docker Desktop
    pause
    exit /b 1
)

REM 检查VPN容器是否运行
docker ps --filter name=vpn-server-container --format "{{.Status}}" | findstr "Up" >nul
if %errorlevel% neq 0 (
    echo ❌ VPN服务器未运行
    echo.
    echo 请选择操作：
    echo 1. 启动VPN服务器
    echo 2. 退出
    echo.
    set /p choice="请输入选项 (1-2): "
    
    if "%choice%"=="1" (
        echo.
        echo [信息] 正在启动VPN服务器...
        call windows-setup.bat
        goto check_again
    ) else (
        exit /b 0
    )
)

:check_again
echo ✅ VPN服务器正在运行

echo.
echo [信息] 正在检查服务端口...

REM 检查端口监听
netstat -an | findstr ":443" >nul
if %errorlevel% equ 0 (
    echo ✅ 端口443正在监听
) else (
    echo ⚠️  端口443未监听，服务可能还在启动中
)

netstat -an | findstr ":80" >nul
if %errorlevel% equ 0 (
    echo ✅ 端口80正在监听
) else (
    echo ⚠️  端口80未监听，服务可能还在启动中
)

echo.
echo [信息] 等待服务完全启动...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo 打开配置网页
echo ========================================
echo.

echo [信息] 正在打开VPN配置网页...
echo.

REM 打开主页
echo 🌐 打开主页...
start http://localhost
echo    ✅ 主页: http://localhost

timeout /t 2 /nobreak >nul

REM 打开二维码页面
echo 📱 打开二维码页面...
start http://localhost/qr
echo    ✅ 二维码: http://localhost/qr

timeout /t 2 /nobreak >nul

REM 打开SS链接页面
echo 🔗 打开SS链接页面...
start http://localhost/ss
echo    ✅ SS链接: http://localhost/ss

echo.
echo ========================================
echo 网页说明
echo ========================================
echo.

echo 📋 已打开的网页说明：
echo.
echo 🏠 主页 (http://localhost)
echo    - 显示项目信息和GitHub链接
echo    - 如果显示GitHub页面说明服务正常
echo.
echo 📱 二维码页面 (http://localhost/qr)
echo    - 显示VPN配置的二维码
echo    - 用手机客户端扫描此二维码可自动配置
echo    - 支持Shadowsocks、Shadowrocket等客户端
echo.
echo 🔗 SS链接页面 (http://localhost/ss)
echo    - 显示SS协议链接
echo    - 复制此链接可导入到客户端
echo    - 格式: ss://base64编码@服务器:端口?插件参数
echo.

echo ========================================
echo 使用指南
echo ========================================
echo.

echo 📱 移动端配置：
echo 1. Android: 安装Shadowsocks + V2Ray插件，扫描二维码
echo 2. iOS: 安装Shadowrocket，扫描二维码或导入SS链接
echo.

echo 💻 桌面端配置：
echo 1. Windows: 下载Shadowsocks-Windows + V2Ray插件
echo 2. macOS: 下载ShadowsocksX-NG
echo 3. Linux: 安装shadowsocks-libev + v2ray-plugin
echo.

echo 🔧 手动配置信息：
docker exec vpn-server-container cat /etc/shadowsocks-libev/config.json 2>nul | findstr -v "^{" | findstr -v "^}" | findstr -v "server_port" | findstr -v "plugin_opts"
echo.

echo ========================================
echo 管理命令
echo ========================================
echo.

echo 🛠️  常用管理命令：
echo - 管理服务器: manage-server.bat
echo - 生成配置: generate-config.bat
echo - 测试服务: test-server.bat
echo - 生成密码: 密码生成器.bat
echo.

echo 🔍 Docker命令：
echo - 查看状态: docker ps
echo - 查看日志: docker logs vpn-server-container
echo - 重启服务: docker restart vpn-server-container
echo - 停止服务: docker stop vpn-server-container
echo.

echo ========================================
echo 故障排除
echo ========================================
echo.

echo 🚨 如果网页无法打开：
echo 1. 检查VPN服务器是否正在运行
echo 2. 确认端口443和80没有被其他程序占用
echo 3. 检查Windows防火墙设置
echo 4. 查看Docker容器日志排查问题
echo.

echo 🔄 如果配置有问题：
echo 1. 运行 test-server.bat 进行诊断
echo 2. 查看 故障排除指南.md 文档
echo 3. 重新生成密码: 密码生成器.bat
echo 4. 重启服务器: manage-server.bat
echo.

echo ========================================
echo 安全提醒
echo ========================================
echo.

echo 🔒 安全建议：
echo - 定期更换VPN密码
echo - 不要将配置信息分享给不信任的人
echo - 监控服务器访问日志
echo - 保持软件更新
echo.

echo 📝 配置文件位置：
echo - 主配置: .env
echo - 备份文件: .env.backup.*
echo - 构建日志: build.log
echo.

echo ✅ 配置网页已全部打开！
echo.
echo 💡 提示：
echo - 如需重新打开网页，可再次运行此脚本
echo - 如需修改配置，请使用 manage-server.bat
echo - 如需帮助，请查看 使用说明.md 文档
echo.

pause
