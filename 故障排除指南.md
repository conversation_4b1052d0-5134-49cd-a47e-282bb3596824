# VPN服务器故障排除指南

## 常见问题及解决方案

### 1. 部署相关问题

#### 问题：Docker构建失败
**症状**：运行`windows-setup.bat`时显示镜像构建失败

**解决方案**：
1. 检查Docker是否正常运行：`docker --version`
2. 确保网络连接正常，能够下载依赖包
3. 检查Dockerfile语法是否正确
4. 清理Docker缓存：`docker system prune -f`
5. 重新构建：`docker build --no-cache -t vpn-server .`

#### 问题：容器启动失败
**症状**：容器无法启动或立即退出

**解决方案**：
1. 查看容器日志：`docker logs vpn-server-container`
2. 检查端口是否被占用：`netstat -an | findstr ":443"`
3. 确认.env文件配置正确
4. 尝试使用不同端口：修改docker run命令中的端口映射

### 2. 网络连接问题

#### 问题：无法访问服务器
**症状**：客户端无法连接到VPN服务器

**解决方案**：
1. **检查服务器状态**：
   ```bash
   docker ps --filter name=vpn-server-container
   ```

2. **检查端口监听**：
   ```bash
   netstat -an | findstr ":443"
   ```

3. **测试本地连接**：
   ```bash
   curl -I http://localhost
   ```

4. **检查防火墙设置**：
   - Windows：确保Windows防火墙允许端口443和80
   - 云服务器：检查安全组设置

#### 问题：HTTPS连接失败
**症状**：HTTP可以访问，但HTTPS失败

**解决方案**：
1. 检查TLS证书配置
2. 确认V2Ray插件正常工作
3. 查看Nginx配置是否正确
4. 检查容器日志中的SSL相关错误

### 3. 客户端配置问题

#### 问题：客户端连接超时
**症状**：客户端显示连接超时或无法连接

**解决方案**：
1. **验证服务器配置**：
   - 服务器地址是否正确
   - 端口是否为443
   - 密码是否匹配

2. **检查插件配置**：
   - 确认v2ray-plugin已安装
   - 插件参数：`path=/v2;host=your-domain;tls`

3. **网络诊断**：
   ```bash
   # 测试服务器连通性
   ping your-domain.com
   
   # 测试端口连通性
   telnet your-domain.com 443
   ```

#### 问题：二维码无法扫描
**症状**：移动端无法识别二维码

**解决方案**：
1. 访问 `http://your-domain/qr` 检查二维码是否生成
2. 确认Domain环境变量设置正确
3. 尝试手动输入配置信息
4. 检查客户端应用是否支持v2ray插件

### 4. 性能问题

#### 问题：连接速度慢
**症状**：VPN连接成功但速度很慢

**解决方案**：
1. **服务器优化**：
   - 选择地理位置更近的服务器
   - 升级服务器配置（CPU、内存、带宽）

2. **配置优化**：
   - 尝试不同的加密方法
   - 调整超时设置
   - 启用fast_open（如果支持）

3. **网络优化**：
   - 检查本地网络质量
   - 尝试不同的DNS服务器
   - 使用CDN加速

#### 问题：频繁断线
**症状**：VPN连接不稳定，经常断开

**解决方案**：
1. 增加超时时间：修改配置中的timeout值
2. 检查服务器资源使用情况
3. 优化keep-alive设置
4. 检查网络环境是否稳定

### 5. Heroku部署问题

#### 问题：Heroku应用被封
**症状**：应用无法访问或被Heroku暂停

**解决方案**：
1. **Fork项目**：
   - Fork项目到自己的GitHub账户
   - 重新部署

2. **使用Heroku CLI**：
   ```bash
   heroku create your-app-name
   heroku stack:set container -a your-app-name
   git push heroku main
   ```

3. **更换部署平台**：
   - 尝试Railway、Render等其他平台
   - 使用自己的VPS服务器

#### 问题：环境变量设置失败
**症状**：部署成功但配置不正确

**解决方案**：
1. 在Heroku Dashboard中检查环境变量
2. 使用Heroku CLI设置：
   ```bash
   heroku config:set Password=your-password -a your-app-name
   heroku config:set Domain=your-app-name.herokuapp.com -a your-app-name
   ```

### 6. 日志分析

#### 查看详细日志
```bash
# Docker日志
docker logs -f vpn-server-container

# Heroku日志
heroku logs --tail -a your-app-name

# 系统日志（Linux）
journalctl -u shadowsocks
```

#### 常见错误信息

1. **"bind: address already in use"**
   - 端口被占用，更换端口或停止占用进程

2. **"connection refused"**
   - 服务未启动或防火墙阻止连接

3. **"plugin not found"**
   - v2ray插件未安装或路径不正确

4. **"invalid password"**
   - 客户端密码与服务器不匹配

### 7. 安全建议

#### 定期维护
1. **更新密码**：每月更换一次VPN密码
2. **监控日志**：定期检查访问日志
3. **更新软件**：保持Docker镜像和依赖包最新

#### 安全配置
1. **使用强密码**：包含大小写字母、数字和特殊字符
2. **限制访问**：不要公开分享配置信息
3. **备份配置**：定期备份重要配置文件

### 8. 获取帮助

如果以上方案都无法解决问题：

1. **收集信息**：
   - 操作系统版本
   - Docker版本
   - 错误日志
   - 配置文件内容

2. **寻求帮助**：
   - 查看项目GitHub Issues
   - 提交新的Issue
   - 参考官方文档

3. **社区支持**：
   - Shadowsocks官方文档
   - V2Ray用户手册
   - Docker官方文档

### 9. 应急方案

#### 快速恢复
如果服务完全无法使用：

1. **重新部署**：
   ```bash
   docker stop vpn-server-container
   docker rm vpn-server-container
   docker rmi vpn-server
   # 重新运行 windows-setup.bat
   ```

2. **使用备用配置**：
   - 恢复默认.env配置
   - 使用测试密码和域名

3. **切换平台**：
   - 如果Heroku有问题，尝试Railway
   - 如果云平台有问题，使用本地Docker

记住：大多数问题都可以通过仔细检查配置和日志来解决。保持耐心，逐步排查！
