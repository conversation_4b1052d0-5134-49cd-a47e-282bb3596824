@echo off
chcp 65001 >nul
echo ========================================
echo VPN密码生成器
echo ========================================
echo.

:menu
echo 请选择密码生成方式：
echo.
echo 1. 自动生成强密码（推荐）
echo 2. 自定义密码长度
echo 3. 生成多个密码供选择
echo 4. 查看当前密码
echo 5. 应用新密码到配置
echo 0. 返回
echo.
set /p choice="请输入选项 (0-5): "

if "%choice%"=="1" goto auto_generate
if "%choice%"=="2" goto custom_length
if "%choice%"=="3" goto multiple_passwords
if "%choice%"=="4" goto show_current
if "%choice%"=="5" goto apply_password
if "%choice%"=="0" goto exit
goto menu

:auto_generate
echo.
echo ========================================
echo 自动生成强密码
echo ========================================
echo.

echo [信息] 正在生成32位强密码...

REM 方法1: 使用GUID方法生成密码（兼容性最好）
for /f "delims=" %%i in ('powershell -Command "[System.Guid]::NewGuid().ToString().Replace('-','') + [System.Guid]::NewGuid().ToString().Replace('-','').Substring(0,8)"') do set GENERATED_PASSWORD=%%i

REM 方法2: 如果方法1失败，使用时间戳方法
if "%GENERATED_PASSWORD%"=="" (
    echo [信息] 使用备用方法生成密码...
    set GENERATED_PASSWORD=VPN_%RANDOM%%RANDOM%%date:~0,4%%time:~0,2%%time:~3,2%%time:~6,2%_%RANDOM%
)

if "%GENERATED_PASSWORD%"=="" (
    echo [错误] 密码生成失败，请检查PowerShell环境
    pause
    goto menu
)

echo [成功] 生成的密码: %GENERATED_PASSWORD%
echo.
echo 密码特性：
echo - 长度: 32位
echo - 包含: 大小写字母、数字、特殊字符
echo - 强度: 高
echo.
set /p apply="是否应用此密码到.env配置文件？(y/n): "
if /i "%apply%"=="y" (
    call :update_env_password "%GENERATED_PASSWORD%"
)
pause
goto menu

:custom_length
echo.
echo ========================================
echo 自定义密码长度
echo ========================================
echo.

set /p length="请输入密码长度 (8-64): "

REM 验证输入
if "%length%"=="" goto custom_length
if %length% lss 8 (
    echo [错误] 密码长度不能少于8位
    goto custom_length
)
if %length% gtr 64 (
    echo [错误] 密码长度不能超过64位
    goto custom_length
)

echo [信息] 正在生成%length%位密码...

for /f "delims=" %%i in ('powershell -Command "$chars='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%%^&*()_+-=[]{}|;:,.<>?'; -join ((1..%length%) | ForEach {$chars[(Get-Random -Maximum $chars.length)]})"') do set CUSTOM_PASSWORD=%%i

if "%CUSTOM_PASSWORD%"=="" (
    echo [错误] 密码生成失败
    pause
    goto menu
)

echo [成功] 生成的%length%位密码: %CUSTOM_PASSWORD%
echo.
set /p apply="是否应用此密码到.env配置文件？(y/n): "
if /i "%apply%"=="y" (
    call :update_env_password "%CUSTOM_PASSWORD%"
)
pause
goto menu

:multiple_passwords
echo.
echo ========================================
echo 生成多个密码供选择
echo ========================================
echo.

echo [信息] 正在生成5个不同的强密码...
echo.

for /L %%i in (1,1,5) do (
    for /f "delims=" %%j in ('powershell -Command "$chars='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%%^&*()_+-=[]{}|;:,.<>?'; -join ((1..32) | ForEach {$chars[(Get-Random -Maximum $chars.length)]})"') do (
        echo %%i. %%j
        set PASSWORD_%%i=%%j
    )
)

echo.
set /p selection="请选择一个密码 (1-5) 或按回车返回: "

if "%selection%"=="" goto menu
if "%selection%"=="1" set SELECTED_PASSWORD=%PASSWORD_1%
if "%selection%"=="2" set SELECTED_PASSWORD=%PASSWORD_2%
if "%selection%"=="3" set SELECTED_PASSWORD=%PASSWORD_3%
if "%selection%"=="4" set SELECTED_PASSWORD=%PASSWORD_4%
if "%selection%"=="5" set SELECTED_PASSWORD=%PASSWORD_5%

if "%SELECTED_PASSWORD%"=="" (
    echo [错误] 无效选择
    pause
    goto multiple_passwords
)

echo.
echo [选择] 您选择的密码: %SELECTED_PASSWORD%
echo.
set /p apply="是否应用此密码到.env配置文件？(y/n): "
if /i "%apply%"=="y" (
    call :update_env_password "%SELECTED_PASSWORD%"
)
pause
goto menu

:show_current
echo.
echo ========================================
echo 当前密码信息
echo ========================================
echo.

if not exist .env (
    echo [错误] .env配置文件不存在
    pause
    goto menu
)

echo [信息] 读取当前配置...
for /f "tokens=2 delims==" %%i in ('findstr "^Password=" .env 2^>nul') do set CURRENT_PASSWORD=%%i

if "%CURRENT_PASSWORD%"=="" (
    echo [警告] 未找到密码配置
) else (
    echo 当前密码: %CURRENT_PASSWORD%
    echo 密码长度:
    powershell -Command "Write-Host ('%CURRENT_PASSWORD%'.Length)"
    echo.
    echo 密码强度分析:
    powershell -Command "$pwd='%CURRENT_PASSWORD%'; $hasUpper=[regex]::IsMatch($pwd,'[A-Z]'); $hasLower=[regex]::IsMatch($pwd,'[a-z]'); $hasDigit=[regex]::IsMatch($pwd,'[0-9]'); $hasSpecial=[regex]::IsMatch($pwd,'[^A-Za-z0-9]'); Write-Host '- 包含大写字母:' $hasUpper; Write-Host '- 包含小写字母:' $hasLower; Write-Host '- 包含数字:' $hasDigit; Write-Host '- 包含特殊字符:' $hasSpecial"
)

echo.
pause
goto menu

:apply_password
echo.
echo ========================================
echo 应用新密码
echo ========================================
echo.

set /p new_password="请输入新密码: "
if "%new_password%"=="" (
    echo [错误] 密码不能为空
    pause
    goto menu
)

call :update_env_password "%new_password%"
pause
goto menu

:update_env_password
set update_password=%~1

echo [信息] 正在更新.env配置文件...

REM 备份原文件
if exist .env (
    copy .env .env.backup.%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2% >nul 2>&1
    echo [信息] 已备份原配置文件
)

REM 更新密码
powershell -Command "(Get-Content .env -Raw) -replace 'Password=.*', 'Password=%update_password%' | Set-Content .env -NoNewline" 2>nul

if %errorlevel% equ 0 (
    echo [成功] 密码已更新到.env文件
    echo [提示] 新密码: %update_password%
    echo [提示] 请重启VPN服务器使新密码生效
) else (
    echo [错误] 更新密码失败，请检查文件权限
)

echo.
goto :eof

:exit
echo.
echo 感谢使用VPN密码生成器！
echo.
echo 提醒：
echo - 请妥善保管您的VPN密码
echo - 建议定期更换密码以提高安全性
echo - 如果更改了密码，请重启VPN服务器
echo.
pause
exit
