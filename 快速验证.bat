@echo off
chcp 65001 >nul
echo ========================================
echo VPN服务器快速验证脚本
echo ========================================
echo.

echo [验证1] 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动
    echo 请先安装Docker Desktop并确保其正常运行
    pause
    exit /b 1
) else (
    echo ✅ Docker环境正常
)

echo.
echo [验证2] 检查项目文件...
set missing_files=0

if not exist "Dockerfile" (
    echo ❌ 缺少 Dockerfile
    set /a missing_files+=1
) else (
    echo ✅ Dockerfile 存在
)

if not exist "entrypoint.sh" (
    echo ❌ 缺少 entrypoint.sh
    set /a missing_files+=1
) else (
    echo ✅ entrypoint.sh 存在
)

if not exist ".env" (
    echo ❌ 缺少 .env 配置文件
    set /a missing_files+=1
) else (
    echo ✅ .env 配置文件存在
)

if not exist "conf\nginx_ss.conf" (
    echo ❌ 缺少 nginx配置文件
    set /a missing_files+=1
) else (
    echo ✅ nginx配置文件存在
)

if not exist "v2" (
    echo ❌ 缺少 v2ray插件
    set /a missing_files+=1
) else (
    echo ✅ v2ray插件存在
)

if %missing_files% gtr 0 (
    echo.
    echo ❌ 发现 %missing_files% 个缺失文件，请检查项目完整性
    pause
    exit /b 1
)

echo.
echo [验证3] 测试Docker镜像构建...
echo 正在构建测试镜像...
docker build -t vpn-verify-test . >build-verify.log 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker镜像构建失败
    echo 错误详情请查看 build-verify.log 文件
    pause
    exit /b 1
) else (
    echo ✅ Docker镜像构建成功
)

echo.
echo [验证4] 测试容器启动...
echo 正在启动测试容器...

REM 停止可能存在的测试容器
docker stop vpn-verify-container >nul 2>&1
docker rm vpn-verify-container >nul 2>&1

REM 启动新的测试容器
docker run -d --name vpn-verify-container -p 9080:443 -p 9081:80 -e Password=verify123 -e Domain=localhost -e PORT=443 vpn-verify-test >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 容器启动失败
    pause
    exit /b 1
)

echo 等待服务启动...
timeout /t 8 /nobreak >nul

REM 检查容器状态
docker ps --filter name=vpn-verify-container --format "{{.Status}}" | findstr "Up" >nul
if %errorlevel% neq 0 (
    echo ❌ 容器未正常运行
    echo 容器日志：
    docker logs vpn-verify-container
    docker stop vpn-verify-container >nul 2>&1
    docker rm vpn-verify-container >nul 2>&1
    pause
    exit /b 1
) else (
    echo ✅ 容器启动成功
)

echo.
echo [验证5] 检查服务组件...

REM 检查Shadowsocks
docker logs vpn-verify-container 2>&1 | findstr "shadowsocks" >nul
if %errorlevel% equ 0 (
    echo ✅ Shadowsocks服务正常
) else (
    echo ⚠️  Shadowsocks状态未知
)

REM 检查V2Ray
docker logs vpn-verify-container 2>&1 | findstr "V2Ray" >nul
if %errorlevel% equ 0 (
    echo ✅ V2Ray插件正常
) else (
    echo ⚠️  V2Ray状态未知
)

REM 检查配置文件生成
docker exec vpn-verify-container test -f /etc/shadowsocks-libev/config.json >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Shadowsocks配置生成正常
) else (
    echo ❌ Shadowsocks配置生成失败
)

docker exec vpn-verify-container test -f /etc/nginx/conf.d/ss.conf >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Nginx配置生成正常
) else (
    echo ❌ Nginx配置生成失败
)

echo.
echo [验证6] 检查功能组件...

REM 检查SS链接生成
docker exec vpn-verify-container test -f /wwwroot/index.html >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SS链接生成正常
) else (
    echo ❌ SS链接生成失败
)

REM 检查QR码生成
docker exec vpn-verify-container test -f /wwwroot/vpn.png >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ QR码生成正常
) else (
    echo ❌ QR码生成失败
)

echo.
echo [验证7] 清理测试环境...
docker stop vpn-verify-container >nul 2>&1
docker rm vpn-verify-container >nul 2>&1
docker rmi vpn-verify-test >nul 2>&1
echo ✅ 测试环境清理完成

echo.
echo ========================================
echo 验证结果总结
echo ========================================
echo.
echo ✅ 所有验证项目都已通过！
echo.
echo 您的VPN服务器项目已经完全配置完成并可以正常使用。
echo.
echo 下一步操作：
echo 1. 运行 windows-setup.bat 开始使用VPN服务器
echo 2. 运行 generate-config.bat 生成客户端配置
echo 3. 运行 manage-server.bat 进行日常管理
echo.
echo 获取帮助：
echo - 查看 使用说明.md 了解详细使用方法
echo - 查看 故障排除指南.md 解决常见问题
echo - 查看 调试报告.md 了解技术细节
echo.
echo 🎉 恭喜！您的VPN服务器已经准备就绪！
echo.
pause
