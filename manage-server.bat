@echo off
chcp 65001 >nul

:menu
cls
echo ========================================
echo VPN服务器管理工具
echo ========================================
echo.
echo 请选择操作：
echo.
echo 1. 启动VPN服务器
echo 2. 停止VPN服务器
echo 3. 重启VPN服务器
echo 4. 查看服务器状态
echo 5. 查看服务器日志
echo 6. 测试服务器连接
echo 7. 生成客户端配置
echo 8. 修改服务器配置
echo 9. 自动生成新密码
echo 10. 打开配置网页
echo 11. 清理Docker资源
echo 0. 退出
echo.
set /p choice="请输入选项 (0-11): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto stop_server
if "%choice%"=="3" goto restart_server
if "%choice%"=="4" goto status_server
if "%choice%"=="5" goto logs_server
if "%choice%"=="6" goto test_server
if "%choice%"=="7" goto generate_config
if "%choice%"=="8" goto edit_config
if "%choice%"=="9" goto auto_password
if "%choice%"=="10" goto open_web
if "%choice%"=="11" goto cleanup_docker
if "%choice%"=="0" goto exit
goto menu

:start_server
echo.
echo ========================================
echo 启动VPN服务器
echo ========================================
call windows-setup.bat
pause
goto menu

:stop_server
echo.
echo ========================================
echo 停止VPN服务器
echo ========================================
echo [信息] 正在停止VPN服务器...
docker stop vpn-server-container 2>nul
if %errorlevel% equ 0 (
    echo [成功] VPN服务器已停止
) else (
    echo [警告] VPN服务器可能未运行
)
pause
goto menu

:restart_server
echo.
echo ========================================
echo 重启VPN服务器
echo ========================================
echo [信息] 正在重启VPN服务器...
docker restart vpn-server-container 2>nul
if %errorlevel% equ 0 (
    echo [成功] VPN服务器已重启
    echo [信息] 等待服务启动...
    timeout /t 5 /nobreak >nul
    docker ps --filter name=vpn-server-container
) else (
    echo [错误] 重启失败，容器可能不存在
    echo [提示] 请先使用选项1启动服务器
)
pause
goto menu

:status_server
echo.
echo ========================================
echo 服务器状态
echo ========================================
echo [信息] Docker容器状态：
docker ps --filter name=vpn-server-container --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.
echo [信息] 端口监听状态：
netstat -an | findstr ":443"
netstat -an | findstr ":80"
echo.
echo [信息] 系统资源使用：
docker stats vpn-server-container --no-stream 2>nul
pause
goto menu

:logs_server
echo.
echo ========================================
echo 服务器日志
echo ========================================
echo [信息] 显示最近50行日志：
echo.
docker logs --tail 50 vpn-server-container 2>nul
echo.
echo [提示] 按任意键返回菜单，或按Ctrl+C查看实时日志
pause
goto menu

:test_server
echo.
echo ========================================
echo 测试服务器
echo ========================================
call test-server.bat
pause
goto menu

:generate_config
echo.
echo ========================================
echo 生成客户端配置
echo ========================================
call generate-config.bat
pause
goto menu

:edit_config
echo.
echo ========================================
echo 修改服务器配置
echo ========================================
echo.
echo 当前配置文件内容：
if exist .env (
    type .env
) else (
    echo [警告] .env文件不存在
)
echo.
echo 请选择操作：
echo 1. 修改密码
echo 2. 修改域名
echo 3. 用记事本编辑.env文件
echo 4. 返回主菜单
echo.
set /p config_choice="请输入选项 (1-4): "

if "%config_choice%"=="1" goto change_password
if "%config_choice%"=="2" goto change_domain
if "%config_choice%"=="3" goto edit_env
if "%config_choice%"=="4" goto menu
goto edit_config

:change_password
echo.
set /p new_password="请输入新密码: "
if "%new_password%"=="" (
    echo [错误] 密码不能为空
    pause
    goto edit_config
)

REM 更新.env文件中的密码
if exist .env (
    powershell -Command "(Get-Content .env) -replace '^Password=.*', 'Password=%new_password%' | Set-Content .env"
) else (
    echo Password=%new_password% > .env
    echo Domain=localhost >> .env
)

echo [成功] 密码已更新为: %new_password%
echo [提示] 请重启服务器使配置生效
pause
goto edit_config

:change_domain
echo.
set /p new_domain="请输入新域名 (不包含http://): "
if "%new_domain%"=="" (
    echo [错误] 域名不能为空
    pause
    goto edit_config
)

REM 更新.env文件中的域名
if exist .env (
    powershell -Command "(Get-Content .env) -replace '^Domain=.*', 'Domain=%new_domain%' | Set-Content .env"
) else (
    echo Password=5c301bb8-6c77-41a0-a606-4ba11bbab084 > .env
    echo Domain=%new_domain% >> .env
)

echo [成功] 域名已更新为: %new_domain%
echo [提示] 请重启服务器使配置生效
pause
goto edit_config

:edit_env
echo.
echo [信息] 正在打开.env文件...
if not exist .env (
    echo # VPN服务器配置文件 > .env
    echo Password=5c301bb8-6c77-41a0-a606-4ba11bbab084 >> .env
    echo Domain=localhost >> .env
)
notepad .env
echo [提示] 如果修改了配置，请重启服务器使配置生效
pause
goto edit_config

:auto_password
echo.
echo ========================================
echo 自动生成新密码
echo ========================================
echo.
echo [警告] 此操作将生成新的VPN密码并更新配置
set /p confirm="确定要继续吗？(y/n): "
if /i "%confirm%" neq "y" goto menu

echo.
echo [信息] 正在生成新密码...

REM 生成强密码（使用GUID方法）
for /f "delims=" %%i in ('powershell -Command "[System.Guid]::NewGuid().ToString().Replace('-','') + [System.Guid]::NewGuid().ToString().Replace('-','').Substring(0,8)"') do set NEW_AUTO_PASSWORD=%%i

REM 如果失败，使用备用方法
if "%NEW_AUTO_PASSWORD%"=="" (
    set NEW_AUTO_PASSWORD=VPN_%RANDOM%%RANDOM%%date:~0,4%%time:~0,2%%time:~3,2%%time:~6,2%_%RANDOM%
)

if "%NEW_AUTO_PASSWORD%"=="" (
    echo [错误] 密码生成失败
    pause
    goto menu
)

echo [成功] 新密码: %NEW_AUTO_PASSWORD%

REM 备份当前配置
if exist .env (
    copy .env .env.backup.%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2% >nul 2>&1
    echo [信息] 已备份当前配置
)

REM 更新密码
powershell -Command "(Get-Content .env -Raw) -replace 'Password=.*', 'Password=%NEW_AUTO_PASSWORD%' | Set-Content .env -NoNewline" 2>nul
if %errorlevel% equ 0 (
    echo [成功] 密码已更新到.env文件
    echo [提示] 请重启服务器使新密码生效
) else (
    echo [错误] 更新密码失败
)

echo.
echo 新的VPN连接信息：
echo - 密码: %NEW_AUTO_PASSWORD%
echo - 其他配置保持不变
echo.
pause
goto menu

:open_web
echo.
echo ========================================
echo 打开配置网页
echo ========================================
echo.

REM 检查服务是否运行
docker ps --filter name=vpn-server-container --format "{{.Status}}" | findstr "Up" >nul
if %errorlevel% neq 0 (
    echo [错误] VPN服务器未运行
    echo 请先启动服务器（选项1）
    pause
    goto menu
)

echo [信息] 正在打开VPN配置网页...

REM 打开主页
start http://localhost
echo [信息] 已打开主页: http://localhost

timeout /t 1 /nobreak >nul

REM 打开二维码页面
start http://localhost/qr
echo [信息] 已打开二维码页面: http://localhost/qr

timeout /t 1 /nobreak >nul

REM 打开SS链接页面
start http://localhost/ss
echo [信息] 已打开SS链接页面: http://localhost/ss

echo.
echo [成功] 所有配置网页已打开
echo.
echo 网页说明：
echo - 主页: 项目信息
echo - /qr: 二维码（手机扫描）
echo - /ss: SS链接（复制到客户端）
echo.
pause
goto menu

:cleanup_docker
echo.
echo ========================================
echo 清理Docker资源
echo ========================================
echo [警告] 此操作将删除VPN相关的Docker容器和镜像
set /p confirm="确定要继续吗？(y/n): "
if /i "%confirm%" neq "y" goto menu

echo [信息] 停止并删除容器...
docker stop vpn-server-container 2>nul
docker rm vpn-server-container 2>nul

echo [信息] 删除镜像...
docker rmi vpn-server 2>nul

echo [信息] 清理未使用的资源...
docker system prune -f

echo [成功] 清理完成
pause
goto menu

:exit
echo.
echo 感谢使用VPN服务器管理工具！
echo.
pause
exit
