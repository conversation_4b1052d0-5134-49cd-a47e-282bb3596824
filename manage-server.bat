@echo off
chcp 65001 >nul

:menu
cls
echo ========================================
echo VPN服务器管理工具
echo ========================================
echo.
echo 请选择操作：
echo.
echo 1. 启动VPN服务器
echo 2. 停止VPN服务器  
echo 3. 重启VPN服务器
echo 4. 查看服务器状态
echo 5. 查看服务器日志
echo 6. 测试服务器连接
echo 7. 生成客户端配置
echo 8. 修改服务器配置
echo 9. 清理Docker资源
echo 0. 退出
echo.
set /p choice="请输入选项 (0-9): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto stop_server
if "%choice%"=="3" goto restart_server
if "%choice%"=="4" goto status_server
if "%choice%"=="5" goto logs_server
if "%choice%"=="6" goto test_server
if "%choice%"=="7" goto generate_config
if "%choice%"=="8" goto edit_config
if "%choice%"=="9" goto cleanup_docker
if "%choice%"=="0" goto exit
goto menu

:start_server
echo.
echo ========================================
echo 启动VPN服务器
echo ========================================
call windows-setup.bat
pause
goto menu

:stop_server
echo.
echo ========================================
echo 停止VPN服务器
echo ========================================
echo [信息] 正在停止VPN服务器...
docker stop vpn-server-container 2>nul
if %errorlevel% equ 0 (
    echo [成功] VPN服务器已停止
) else (
    echo [警告] VPN服务器可能未运行
)
pause
goto menu

:restart_server
echo.
echo ========================================
echo 重启VPN服务器
echo ========================================
echo [信息] 正在重启VPN服务器...
docker restart vpn-server-container 2>nul
if %errorlevel% equ 0 (
    echo [成功] VPN服务器已重启
    echo [信息] 等待服务启动...
    timeout /t 5 /nobreak >nul
    docker ps --filter name=vpn-server-container
) else (
    echo [错误] 重启失败，容器可能不存在
    echo [提示] 请先使用选项1启动服务器
)
pause
goto menu

:status_server
echo.
echo ========================================
echo 服务器状态
echo ========================================
echo [信息] Docker容器状态：
docker ps --filter name=vpn-server-container --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.
echo [信息] 端口监听状态：
netstat -an | findstr ":443"
netstat -an | findstr ":80"
echo.
echo [信息] 系统资源使用：
docker stats vpn-server-container --no-stream 2>nul
pause
goto menu

:logs_server
echo.
echo ========================================
echo 服务器日志
echo ========================================
echo [信息] 显示最近50行日志：
echo.
docker logs --tail 50 vpn-server-container 2>nul
echo.
echo [提示] 按任意键返回菜单，或按Ctrl+C查看实时日志
pause
goto menu

:test_server
echo.
echo ========================================
echo 测试服务器
echo ========================================
call test-server.bat
pause
goto menu

:generate_config
echo.
echo ========================================
echo 生成客户端配置
echo ========================================
call generate-config.bat
pause
goto menu

:edit_config
echo.
echo ========================================
echo 修改服务器配置
echo ========================================
echo.
echo 当前配置文件内容：
if exist .env (
    type .env
) else (
    echo [警告] .env文件不存在
)
echo.
echo 请选择操作：
echo 1. 修改密码
echo 2. 修改域名
echo 3. 用记事本编辑.env文件
echo 4. 返回主菜单
echo.
set /p config_choice="请输入选项 (1-4): "

if "%config_choice%"=="1" goto change_password
if "%config_choice%"=="2" goto change_domain
if "%config_choice%"=="3" goto edit_env
if "%config_choice%"=="4" goto menu
goto edit_config

:change_password
echo.
set /p new_password="请输入新密码: "
if "%new_password%"=="" (
    echo [错误] 密码不能为空
    pause
    goto edit_config
)

REM 更新.env文件中的密码
if exist .env (
    powershell -Command "(Get-Content .env) -replace '^Password=.*', 'Password=%new_password%' | Set-Content .env"
) else (
    echo Password=%new_password% > .env
    echo Domain=localhost >> .env
)

echo [成功] 密码已更新为: %new_password%
echo [提示] 请重启服务器使配置生效
pause
goto edit_config

:change_domain
echo.
set /p new_domain="请输入新域名 (不包含http://): "
if "%new_domain%"=="" (
    echo [错误] 域名不能为空
    pause
    goto edit_config
)

REM 更新.env文件中的域名
if exist .env (
    powershell -Command "(Get-Content .env) -replace '^Domain=.*', 'Domain=%new_domain%' | Set-Content .env"
) else (
    echo Password=5c301bb8-6c77-41a0-a606-4ba11bbab084 > .env
    echo Domain=%new_domain% >> .env
)

echo [成功] 域名已更新为: %new_domain%
echo [提示] 请重启服务器使配置生效
pause
goto edit_config

:edit_env
echo.
echo [信息] 正在打开.env文件...
if not exist .env (
    echo # VPN服务器配置文件 > .env
    echo Password=5c301bb8-6c77-41a0-a606-4ba11bbab084 >> .env
    echo Domain=localhost >> .env
)
notepad .env
echo [提示] 如果修改了配置，请重启服务器使配置生效
pause
goto edit_config

:cleanup_docker
echo.
echo ========================================
echo 清理Docker资源
echo ========================================
echo [警告] 此操作将删除VPN相关的Docker容器和镜像
set /p confirm="确定要继续吗？(y/n): "
if /i "%confirm%" neq "y" goto menu

echo [信息] 停止并删除容器...
docker stop vpn-server-container 2>nul
docker rm vpn-server-container 2>nul

echo [信息] 删除镜像...
docker rmi vpn-server 2>nul

echo [信息] 清理未使用的资源...
docker system prune -f

echo [成功] 清理完成
pause
goto menu

:exit
echo.
echo 感谢使用VPN服务器管理工具！
echo.
pause
exit
