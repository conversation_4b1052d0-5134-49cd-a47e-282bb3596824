@echo off
chcp 65001 >nul
echo ========================================
echo VPN服务器测试脚本
echo ========================================
echo.

REM 检查Docker容器状态
echo [测试1] 检查Docker容器状态...
docker ps --filter name=vpn-server-container --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>nul
if %errorlevel% neq 0 (
    echo [失败] Docker容器未运行或不存在
    echo 请先运行 windows-setup.bat 启动服务器
    pause
    exit /b 1
)

echo [成功] Docker容器正在运行
echo.

REM 检查端口监听
echo [测试2] 检查端口监听状态...
netstat -an | findstr ":443" >nul
if %errorlevel% neq 0 (
    echo [警告] 端口443未监听，可能需要等待服务启动
) else (
    echo [成功] 端口443正在监听
)

netstat -an | findstr ":80" >nul
if %errorlevel% neq 0 (
    echo [警告] 端口80未监听
) else (
    echo [成功] 端口80正在监听
)
echo.

REM 测试HTTP连接
echo [测试3] 测试HTTP连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost 2>nul
if %errorlevel% neq 0 (
    echo [警告] HTTP连接测试失败，curl命令不可用或服务未响应
) else (
    echo [成功] HTTP连接正常
)
echo.

REM 测试HTTPS连接（如果支持）
echo [测试4] 测试HTTPS连接...
curl -k -s -o nul -w "HTTPS状态码: %%{http_code}\n" https://localhost 2>nul
if %errorlevel% neq 0 (
    echo [警告] HTTPS连接测试失败
) else (
    echo [成功] HTTPS连接正常
)
echo.

REM 检查配置文件
echo [测试5] 检查配置文件...
if exist .env (
    echo [成功] .env配置文件存在
    echo 配置内容：
    type .env | findstr /v "^#" | findstr /v "^$"
) else (
    echo [警告] .env配置文件不存在
)
echo.

REM 检查Docker镜像
echo [测试6] 检查Docker镜像...
docker images vpn-server --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" 2>nul
if %errorlevel% neq 0 (
    echo [失败] VPN服务器镜像不存在
) else (
    echo [成功] VPN服务器镜像存在
)
echo.

REM 检查容器日志
echo [测试7] 检查容器日志（最近10行）...
docker logs --tail 10 vpn-server-container 2>nul
if %errorlevel% neq 0 (
    echo [失败] 无法获取容器日志
) else (
    echo [成功] 容器日志获取正常
)
echo.

REM 测试SS链接生成
echo [测试8] 测试SS链接访问...
curl -s http://localhost/ss 2>nul | findstr "ss://" >nul
if %errorlevel% neq 0 (
    echo [警告] SS链接生成可能有问题
) else (
    echo [成功] SS链接生成正常
)
echo.

REM 测试QR码生成
echo [测试9] 测试QR码访问...
curl -s -I http://localhost/qr 2>nul | findstr "200 OK" >nul
if %errorlevel% neq 0 (
    echo [警告] QR码生成可能有问题
) else (
    echo [成功] QR码访问正常
)
echo.

echo ========================================
echo 测试完成
echo ========================================
echo.

echo 如果所有测试都通过，您的VPN服务器应该可以正常使用了！
echo.
echo 客户端配置信息：
echo - 服务器地址: localhost 或 127.0.0.1
echo - 端口: 443
echo - 加密方式: chacha20-ietf-poly1305
echo - 插件: v2ray-plugin
echo - 插件参数: path=/v2;host=localhost;tls
echo.

echo 获取配置的方法：
echo 1. 访问 http://localhost/ss 获取SS链接
echo 2. 访问 http://localhost/qr 获取二维码
echo 3. 使用上面的手动配置信息
echo.

echo 如果测试失败，请：
echo 1. 检查Docker是否正常运行
echo 2. 确认防火墙没有阻止端口443和80
echo 3. 查看容器日志: docker logs vpn-server-container
echo 4. 重新运行 windows-setup.bat
echo.

pause
