@echo off
chcp 65001 >nul
echo ========================================
echo VPN客户端配置生成器
echo ========================================
echo.

REM 读取.env文件获取配置
set VPN_PASSWORD=5c301bb8-6c77-41a0-a606-4ba11bbab084
set VPN_DOMAIN=localhost

if exist .env (
    echo [信息] 读取.env配置文件...
    for /f "tokens=1,2 delims==" %%a in (.env) do (
        if "%%a"=="Password" set VPN_PASSWORD=%%b
        if "%%a"=="Domain" set VPN_DOMAIN=%%b
    )
) else (
    echo [警告] 未找到.env文件，使用默认配置
)

echo 当前配置：
echo - 服务器: %VPN_DOMAIN%
echo - 密码: %VPN_PASSWORD%
echo.

REM 生成Shadowsocks配置文件
echo [生成] Shadowsocks客户端配置文件...
(
echo {
echo     "server": "%VPN_DOMAIN%",
echo     "server_port": 443,
echo     "local_address": "127.0.0.1",
echo     "local_port": 1080,
echo     "password": "%VPN_PASSWORD%",
echo     "timeout": 300,
echo     "method": "chacha20-ietf-poly1305",
echo     "mode": "tcp_only",
echo     "fast_open": false,
echo     "reuse_port": true,
echo     "no_delay": true,
echo     "plugin": "v2ray-plugin",
echo     "plugin_opts": "path=/v2;host=%VPN_DOMAIN%;tls",
echo     "remarks": "Personal VPN"
echo }
) > shadowsocks-config.json

echo [成功] 已生成 shadowsocks-config.json

REM 生成Windows客户端配置
echo.
echo [生成] Windows Shadowsocks配置...
(
echo {
echo     "configs": [
echo         {
echo             "server": "%VPN_DOMAIN%",
echo             "server_port": 443,
echo             "password": "%VPN_PASSWORD%",
echo             "method": "chacha20-ietf-poly1305",
echo             "plugin": "v2ray-plugin",
echo             "plugin_opts": "path=/v2;host=%VPN_DOMAIN%;tls",
echo             "plugin_args": "",
echo             "remarks": "Personal VPN",
echo             "timeout": 300
echo         }
echo     ],
echo     "strategy": null,
echo     "index": 0,
echo     "global": false,
echo     "enabled": true,
echo     "shareOverLan": false,
echo     "isDefault": false,
echo     "localPort": 1080,
echo     "pacUrl": null,
echo     "useOnlinePac": false,
echo     "secureLocalPac": true,
echo     "availabilityStatistics": false,
echo     "autoCheckUpdate": true,
echo     "checkPreRelease": false,
echo     "isVerboseLogging": false,
echo     "logViewer": {
echo         "topMost": false,
echo         "wrapText": false,
echo         "toolbarShown": false,
echo         "menuShown": false
echo     },
echo     "proxy": {
echo         "useProxy": false,
echo         "proxyType": 0,
echo         "proxyServer": "",
echo         "proxyPort": 0,
echo         "proxyTimeout": 3
echo     },
echo     "hotkey": {
echo         "SwitchSystemProxy": "",
echo         "SwitchSystemProxyMode": "",
echo         "SwitchAllowLan": "",
echo         "ShowLogs": "",
echo         "ServerMoveUp": "",
echo         "ServerMoveDown": ""
echo     }
echo }
) > shadowsocks-windows-config.json

echo [成功] 已生成 shadowsocks-windows-config.json

REM 生成移动端配置信息
echo.
echo [生成] 移动端配置信息...
(
echo ========================================
echo 移动端配置信息
echo ========================================
echo.
echo Android Shadowsocks配置：
echo - 服务器: %VPN_DOMAIN%
echo - 远程端口: 443
echo - 密码: %VPN_PASSWORD%
echo - 加密方法: chacha20-ietf-poly1305
echo - 插件: v2ray
echo - 插件配置: path=/v2;host=%VPN_DOMAIN%;tls
echo.
echo iOS Shadowrocket配置：
echo - 类型: Shadowsocks
echo - 地址: %VPN_DOMAIN%
echo - 端口: 443
echo - 密码: %VPN_PASSWORD%
echo - 算法: chacha20-ietf-poly1305
echo - 插件: v2ray-plugin
echo - 插件参数: path=/v2;host=%VPN_DOMAIN%;tls
echo.
echo 二维码获取：
echo 访问 http://%VPN_DOMAIN%/qr 获取二维码
echo.
echo SS链接获取：
echo 访问 http://%VPN_DOMAIN%/ss 获取SS链接
echo.
) > mobile-config.txt

echo [成功] 已生成 mobile-config.txt

REM 生成Linux配置
echo.
echo [生成] Linux客户端配置...
(
echo #!/bin/bash
echo # Linux Shadowsocks客户端启动脚本
echo.
echo # 安装依赖（Ubuntu/Debian）
echo # sudo apt-get update
echo # sudo apt-get install shadowsocks-libev
echo.
echo # 下载v2ray插件
echo # wget https://github.com/shadowsocks/v2ray-plugin/releases/download/v1.3.1/v2ray-plugin-linux-amd64-v1.3.1.tar.gz
echo # tar -xzf v2ray-plugin-linux-amd64-v1.3.1.tar.gz
echo # sudo mv v2ray-plugin_linux_amd64 /usr/bin/v2ray-plugin
echo.
echo # 启动客户端
echo ss-local -c shadowsocks-config.json -v
echo.
echo # 设置系统代理（可选）
echo # export http_proxy=http://127.0.0.1:1080
echo # export https_proxy=http://127.0.0.1:1080
) > linux-start.sh

echo [成功] 已生成 linux-start.sh

echo.
echo ========================================
echo 配置文件生成完成
echo ========================================
echo.

echo 已生成以下配置文件：
echo 1. shadowsocks-config.json - 通用Shadowsocks配置
echo 2. shadowsocks-windows-config.json - Windows客户端配置
echo 3. mobile-config.txt - 移动端配置说明
echo 4. linux-start.sh - Linux启动脚本
echo.

echo 使用方法：
echo.
echo Windows:
echo 1. 下载Shadowsocks-Windows客户端
echo 2. 导入 shadowsocks-windows-config.json
echo 3. 下载v2ray-plugin插件到同一目录
echo.
echo Android:
echo 1. 安装Shadowsocks Android应用
echo 2. 安装v2ray插件
echo 3. 扫描二维码或手动输入配置
echo.
echo iOS:
echo 1. 购买并安装Shadowrocket
echo 2. 扫描二维码或手动输入配置
echo.
echo Linux:
echo 1. 安装shadowsocks-libev
echo 2. 下载v2ray插件
echo 3. 运行 bash linux-start.sh
echo.

echo 获取最新配置：
echo - 二维码: http://%VPN_DOMAIN%/qr
echo - SS链接: http://%VPN_DOMAIN%/ss
echo.

pause
