@echo off
chcp 65001 >nul
echo ========================================
echo VPN服务器 Windows 配置脚本
echo ========================================
echo.

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Docker，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo [信息] Docker已安装，版本信息：
docker --version

echo.
echo ========================================
echo 配置环境变量
echo ========================================

REM 读取.env文件中的配置
if exist .env (
    echo [信息] 发现.env配置文件
    for /f "tokens=1,2 delims==" %%a in (.env) do (
        if "%%a"=="Password" set VPN_PASSWORD=%%b
        if "%%a"=="Domain" set VPN_DOMAIN=%%b
    )
) else (
    echo [警告] 未找到.env文件，使用默认配置
    set VPN_PASSWORD=5c301bb8-6c77-41a0-a606-4ba11bbab084
    set VPN_DOMAIN=localhost
)

echo 当前配置：
echo - 密码: %VPN_PASSWORD%
echo - 域名: %VPN_DOMAIN%
echo - 端口: 443

echo.
set /p confirm="是否使用以上配置启动服务器？(y/n): "
if /i "%confirm%" neq "y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo ========================================
echo 构建Docker镜像
echo ========================================

echo [信息] 正在构建VPN服务器镜像...
docker build -t vpn-server . 2>build.log
if %errorlevel% neq 0 (
    echo [错误] 镜像构建失败，请检查build.log文件
    echo [提示] 常见问题：
    echo   1. 检查Docker是否正常运行
    echo   2. 确保网络连接正常
    echo   3. 尝试清理Docker缓存: docker system prune -f
    pause
    exit /b 1
)

echo [成功] 镜像构建完成

echo.
echo ========================================
echo 启动VPN服务器
echo ========================================

REM 停止并删除已存在的容器
docker stop vpn-server-container >nul 2>&1
docker rm vpn-server-container >nul 2>&1

echo [信息] 正在启动VPN服务器容器...
docker run -d ^
    --name vpn-server-container ^
    -p 443:443 ^
    -p 80:80 ^
    -e Password=%VPN_PASSWORD% ^
    -e Domain=%VPN_DOMAIN% ^
    -e PORT=443 ^
    vpn-server

if %errorlevel% neq 0 (
    echo [错误] 容器启动失败
    pause
    exit /b 1
)

echo [成功] VPN服务器已启动

echo.
echo ========================================
echo 服务器信息
echo ========================================

echo VPN服务器已成功启动！
echo.
echo 访问地址：
echo - 主页: https://localhost
echo - 二维码: https://localhost/qr
echo - SS链接: https://localhost/ss
echo.
echo 客户端配置：
echo - 服务器: localhost
echo - 端口: 443
echo - 密码: %VPN_PASSWORD%
echo - 加密: chacha20-ietf-poly1305
echo - 插件: v2ray-plugin
echo - 插件选项: path=/v2;host=localhost;tls
echo.

echo ========================================
echo 管理命令
echo ========================================
echo 查看日志: docker logs vpn-server-container
echo 停止服务: docker stop vpn-server-container
echo 重启服务: docker restart vpn-server-container
echo 删除容器: docker rm -f vpn-server-container
echo.

echo [提示] 请保持此窗口打开以查看服务状态
echo [提示] 按Ctrl+C可以查看实时日志

REM 显示容器状态
echo.
echo 当前容器状态：
docker ps --filter name=vpn-server-container

echo.
echo 按任意键查看实时日志...
pause >nul

REM 显示实时日志
docker logs -f vpn-server-container
