# VPN服务器使用说明

## 项目简介

这是一个基于Shadowsocks和V2Ray插件的个人VPN解决方案，支持一键部署到各种云平台。

### 主要特性

- 🔒 **安全加密**: 使用chacha20-ietf-poly1305加密算法
- 🌐 **WebSocket传输**: 通过V2Ray插件支持WebSocket+TLS
- 🚀 **一键部署**: 支持Heroku、Railway等云平台
- 📱 **多平台客户端**: 支持Windows、Android、iOS、Linux、macOS
- 🔧 **易于配置**: 自动生成客户端配置和二维码

## 快速开始

### 🚀 一键自动配置（推荐）

**最简单的方式 - 自动生成密码并启动服务：**

1. **双击运行** `自动配置启动.bat`
2. **自动完成**：
   - 自动生成强密码
   - 自动构建Docker镜像
   - 自动启动VPN服务器
   - 自动打开配置网页
3. **扫描二维码** 或复制SS链接到客户端

### 📱 其他便捷工具

- **打开配置网页.bat** - 快速打开所有配置网页
- **密码生成器.bat** - 生成和管理VPN密码
- **manage-server.bat** - 服务器管理工具（启动、停止、重启等）
- **test-server.bat** - 服务器连接测试工具

### 🛠️ 手动配置方式

### 1. 环境变量配置

在部署前，请修改`.env`文件中的配置：

```bash
# 修改密码（必须）
Password=your-strong-password-here

# 部署后修改域名（必须）
Domain=your-app-domain.com
```

### 2. 部署到云平台

#### Heroku部署
1. 点击README中的Heroku部署按钮
2. 填写应用名称
3. 设置环境变量：
   - `Password`: 您的VPN密码
   - `Domain`: 部署后的域名（如：your-app.herokuapp.com）
4. 点击"Deploy app"

#### Railway部署
1. 点击README中的Railway部署按钮
2. 连接GitHub账户
3. 设置环境变量
4. 部署完成

#### Docker本地部署
```bash
# 构建镜像
docker build -t vpn-server .

# 运行容器
docker run -d -p 443:443 \
  -e Password=your-password \
  -e Domain=your-domain.com \
  vpn-server
```

### 3. 验证部署

部署完成后：
1. 访问 `https://your-domain.com` 应显示正常页面
2. 访问 `https://your-domain.com/static` 应显示404页面（表示部署成功）

## 客户端配置

### 获取配置信息

部署成功后，您可以通过以下方式获取客户端配置：

1. **二维码**: 访问 `https://your-domain.com/qr`
2. **SS链接**: 访问 `https://your-domain.com/ss`
3. **手动配置**: 使用下面的配置模板

### 手动配置模板

```json
{
    "server": "your-domain.com",
    "server_port": 443,
    "local_port": 1080,
    "password": "your-password",
    "timeout": 300,
    "method": "chacha20-ietf-poly1305",
    "mode": "tcp_only",
    "fast_open": false,
    "reuse_port": true,
    "no_delay": true,
    "plugin": "v2ray-plugin",
    "plugin_opts": "path=/v2;host=your-domain.com;tls",
    "remarks": "Personal VPN"
}
```

## 客户端软件

### Windows
- [Shadowsocks-Windows](https://github.com/shadowsocks/shadowsocks-windows/releases)
- [V2Ray插件](https://github.com/shadowsocks/v2ray-plugin/releases)

### Android
- [Shadowsocks Android](https://play.google.com/store/apps/details?id=com.github.shadowsocks)
- [V2Ray插件](https://play.google.com/store/apps/details?id=com.github.shadowsocks.plugin.v2ray)

### iOS
- [Shadowrocket](https://apps.apple.com/app/shadowrocket/id932747118) (付费)
- [Quantumult X](https://apps.apple.com/app/quantumult-x/id1443988620) (付费)

### macOS
- [ShadowsocksX-NG](https://github.com/shadowsocks/ShadowsocksX-NG/releases)

### Linux
```bash
# 安装shadowsocks-libev
sudo apt-get install shadowsocks-libev

# 下载v2ray插件
wget https://github.com/shadowsocks/v2ray-plugin/releases/download/v1.3.1/v2ray-plugin-linux-amd64-v1.3.1.tar.gz
tar -xzf v2ray-plugin-linux-amd64-v1.3.1.tar.gz
sudo mv v2ray-plugin_linux_amd64 /usr/bin/v2ray-plugin

# 启动客户端
ss-local -c config.json
```

## 故障排除

### 常见问题

1. **无法连接**
   - 检查域名是否正确
   - 确认密码是否匹配
   - 验证端口443是否开放

2. **速度慢**
   - 尝试更换服务器地区
   - 检查本地网络状况
   - 考虑使用CDN加速

3. **Heroku应用被封**
   - Fork项目到自己的GitHub
   - 重新部署
   - 或使用Heroku CLI部署

### 日志查看

```bash
# Heroku日志
heroku logs --tail -a your-app-name

# Docker日志
docker logs container-name
```

## 安全建议

1. **定期更换密码**: 建议每月更换一次VPN密码
2. **使用强密码**: 密码应包含字母、数字和特殊字符
3. **限制访问**: 不要将配置信息分享给不信任的人
4. **监控使用**: 定期检查服务器日志和流量使用情况

## 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查项目的Issues页面
3. 提交新的Issue并详细描述问题

## 免责声明

本项目仅供学习和研究使用，请遵守当地法律法规。使用者需对自己的行为负责。
