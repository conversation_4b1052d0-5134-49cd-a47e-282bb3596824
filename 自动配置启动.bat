@echo off
chcp 65001 >nul
echo ========================================
echo VPN服务器自动配置启动器
echo ========================================
echo.

REM 检查PowerShell是否可用
powershell -Command "Write-Host '检查PowerShell...'" >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] PowerShell不可用，无法自动生成密码
    echo 请手动编辑.env文件设置密码
    pause
    exit /b 1
)

echo [步骤1] 自动生成强密码...

REM 生成强密码（使用GUID方法，兼容性最好）
echo [信息] 使用GUID方法生成强密码...
for /f "delims=" %%i in ('powershell -Command "[System.Guid]::NewGuid().ToString().Replace('-','') + [System.Guid]::NewGuid().ToString().Replace('-','').Substring(0,8)"') do set NEW_PASSWORD=%%i

REM 如果GUID方法失败，使用时间戳作为备用
if "%NEW_PASSWORD%"=="" (
    echo [信息] 使用备用方法生成密码...
    set NEW_PASSWORD=VPN_%RANDOM%%RANDOM%%date:~0,4%%time:~0,2%%time:~3,2%%time:~6,2%_%RANDOM%
)

echo [成功] 生成新密码: %NEW_PASSWORD%

echo.
echo [步骤2] 更新.env配置文件...

REM 备份原始.env文件
if exist .env (
    copy .env .env.backup >nul 2>&1
    echo [信息] 已备份原始配置文件为 .env.backup
)

REM 更新.env文件中的密码
powershell -Command "(Get-Content .env -Raw) -replace 'Password=.*', 'Password=%NEW_PASSWORD%' | Set-Content .env -NoNewline"

if %errorlevel% equ 0 (
    echo [成功] 密码已更新到.env文件
) else (
    echo [错误] 更新.env文件失败
    pause
    exit /b 1
)

echo.
echo [步骤3] 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker未安装或未启动
    echo 请先安装Docker Desktop并确保其正常运行
    echo.
    echo 您仍然可以查看生成的配置信息
    goto show_config
)

echo [成功] Docker环境正常

echo.
echo [步骤4] 构建并启动VPN服务器...
echo 正在构建Docker镜像...

docker build -t vpn-server . >build.log 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker镜像构建失败
    echo 错误详情请查看 build.log 文件
    echo.
    echo 您仍然可以查看生成的配置信息
    goto show_config
)

echo [成功] Docker镜像构建完成

REM 停止并删除已存在的容器
docker stop vpn-server-container >nul 2>&1
docker rm vpn-server-container >nul 2>&1

echo 正在启动VPN服务器容器...
docker run -d ^
    --name vpn-server-container ^
    -p 443:443 ^
    -p 80:80 ^
    -e Password=%NEW_PASSWORD% ^
    -e Domain=localhost ^
    -e PORT=443 ^
    vpn-server

if %errorlevel% neq 0 (
    echo [错误] 容器启动失败
    goto show_config
)

echo [成功] VPN服务器已启动

echo.
echo [步骤5] 等待服务启动...
echo 等待10秒让服务完全启动...
timeout /t 10 /nobreak >nul

REM 检查容器状态
docker ps --filter name=vpn-server-container --format "{{.Status}}" | findstr "Up" >nul
if %errorlevel% neq 0 (
    echo [警告] 容器可能未正常运行，请检查日志
    echo 查看日志命令: docker logs vpn-server-container
    echo.
)

:show_config
echo.
echo ========================================
echo 配置信息
echo ========================================
echo.
echo 🔐 VPN连接信息：
echo - 服务器地址: localhost
echo - 端口: 443
echo - 密码: %NEW_PASSWORD%
echo - 加密方法: chacha20-ietf-poly1305
echo - 插件: v2ray-plugin
echo - 插件参数: path=/v2;host=localhost;tls
echo.

echo 📱 客户端配置方法：
echo 1. 自动配置 - 扫描二维码
echo 2. 手动配置 - 使用上面的连接信息
echo 3. SS链接 - 复制SS链接导入客户端
echo.

echo 🌐 网页访问地址：
echo - 主页: http://localhost
echo - 二维码: http://localhost/qr
echo - SS链接: http://localhost/ss
echo.

echo [步骤6] 自动打开网页...

REM 检查服务是否运行
docker ps --filter name=vpn-server-container --format "{{.Status}}" | findstr "Up" >nul
if %errorlevel% equ 0 (
    echo 正在打开VPN配置网页...

    REM 等待2秒确保服务完全启动
    timeout /t 2 /nobreak >nul

    REM 打开主页
    start http://localhost

    REM 等待1秒
    timeout /t 1 /nobreak >nul

    REM 打开二维码页面
    start http://localhost/qr

    REM 等待1秒
    timeout /t 1 /nobreak >nul

    REM 打开SS链接页面
    start http://localhost/ss

    echo [成功] 已自动打开配置网页
    echo.
    echo 📋 网页说明：
    echo - 第1个页面: 项目主页
    echo - 第2个页面: 二维码（用手机扫描）
    echo - 第3个页面: SS链接（复制到客户端）

) else (
    echo [提示] 服务未运行，无法打开网页
    echo 请先解决Docker相关问题，然后重新运行此脚本
)

echo.
echo ========================================
echo 完成信息
echo ========================================
echo.

echo ✅ 自动配置完成！
echo.
echo 📝 重要信息已保存：
echo - 新密码已保存到 .env 文件
echo - 原配置已备份为 .env.backup
echo - 构建日志保存在 build.log
echo.

echo 🔧 管理命令：
echo - 查看服务状态: docker ps
echo - 查看服务日志: docker logs vpn-server-container
echo - 停止服务: docker stop vpn-server-container
echo - 重启服务: docker restart vpn-server-container
echo.

echo 📱 客户端下载：
echo - Windows: Shadowsocks-Windows + V2Ray插件
echo - Android: Shadowsocks + V2Ray插件 (Google Play)
echo - iOS: Shadowrocket 或 Quantumult X (App Store)
echo.

echo 🎯 下次启动：
echo - 运行 manage-server.bat 进行管理
echo - 运行 generate-config.bat 重新生成配置
echo - 运行 test-server.bat 测试服务
echo.

echo 🔒 安全提醒：
echo - 请妥善保管您的VPN密码
echo - 不要将配置信息分享给不信任的人
echo - 建议定期更换密码
echo.

echo 按任意键退出...
pause >nul
